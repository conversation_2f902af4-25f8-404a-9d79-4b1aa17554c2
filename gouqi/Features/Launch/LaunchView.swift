import SwiftUI
import ComposableArchitecture
import LogCore

public struct LaunchView: View {
    let store: StoreOf<LaunchFeature>
  
    public var body: some View {
        WithViewStore(self.store, observe: { $0 }) { viewStore in
            AppBaseView(backgroundColor: Color.colorBackground) {
                HStack(spacing: 10) {
                    Image("icon")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 80, height: 80)
                    Image("icon_text")
                        .resizable()
                        .aspectRatio(92.0/56.0, contentMode: .fit)
                        .frame(height: 80)
                }
            }
            .onAppear {
                // 使用DispatchQueue.main.async避免在视图更新期间修改状态
                DispatchQueue.main.async {
                    viewStore.send(.onAppear)
                }
            }
        }
    }
} 
