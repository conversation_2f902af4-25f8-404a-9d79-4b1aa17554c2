import ComposableArchitecture
import LogCore
import SwiftUI
import DataRepositoryCore
import NetworkCore

// 导入导航上下文（从 AppFeature 中）

@Reducer
struct RecommendFeature {
    public static var pageSize = 30 // 允许外部设置
    // MARK: - 子State定义

    /// 内容列表相关状态
    @ObservableState
    struct ContentState: Equatable {
        var items: [DataRepositoryCore.RecommendCardItem] = []
        var pageState: PageState = .success
        var currentPage: Int = 0
        var hasMore: Bool = true
        var isRefreshing: Bool = false
        var isLoadingMore: Bool = false
        var loadMoreFailed: Bool = false
        var isEmptyList: Bool = false
    }

    /// 搜索相关状态
    @ObservableState
    struct SearchState: Equatable {
        var searchText: String = ""
        var suggestions: [String] = []
        var currentPlaceholder: String = "搜索"
        var lastUsedIndex: Int = -1
        var isFocused: Bool = false
    }

    /// 用户相关状态
    @ObservableState
    struct UserState: Equatable {
        var isLoggedIn: Bool = false
        var avatarUrl: URL? = nil
    }

    /// UI交互状态
    @ObservableState
    struct UIState: Equatable {
        var isTabActive: Bool = true
        var toastMessage: String? = nil
        var hasInitialized: Bool = false // 跟踪是否已完成初始化
        var initializationTimestamp: Date? = nil // 初始化时间戳
        var scrollPosition: CGPoint = .zero // 保存滚动位置
        var shouldRestoreScrollPosition: Bool = false // 是否需要恢复滚动位置
    }

    /// 主State - 组合各个子State
    @ObservableState
    struct State: Equatable {
        var content = ContentState()
        var search = SearchState()
        var user = UserState()
        var ui = UIState()
    }
    
    @Dependency(\.postRepository) var postRepository
    @Dependency(\.userRepository) var userRepository
    @Dependency(\.characterRepository) var characterRepository
    
    enum Action: BindableAction, Equatable {
        case binding(BindingAction<State>)
     
        case refresh(completion: (RefreshResult) -> Void)
        case loadMore(completion: (LoadMoreResult) -> Void)
        case cardTapped(DataRepositoryCore.RecommendCardItem)
        case filterButtonTapped
        case onAppear
        case onDisappear
        case setTabActive(Bool)
        case dismissToast
        
        // 搜索相关动作
        case fetchSearchSuggestions
        case searchButtonTapped
        case refreshSearchPlaceholder
        
        // 用户相关动作
        case loginButtonTapped
        case profileButtonTapped
        
        // 添加delegate用于向父组件通知
        case delegate(Delegate)
        
        // 私有 Action，仅在 Reducer 内部使用
        case _handleRefreshResult(Result, completion: (RefreshResult) -> Void)
        case _handleLoadMoreResult(Result, completion: (LoadMoreResult) -> Void)
        case _searchSuggestionsLoaded([String])

        @CasePathable
        enum Delegate: Equatable {
            case loginRequested(context: NavigationContext)
            case navigateToProfile
        }

        // 手动实现Equatable协议，因为包含闭包的Action无法自动生成
        static func == (lhs: Action, rhs: Action) -> Bool {
            switch (lhs, rhs) {
            case (.binding(let a), .binding(let b)):
                return a == b
            case (.refresh, .refresh):
                return false // 闭包无法比较，总是返回false
            case (.loadMore, .loadMore):
                return false // 闭包无法比较，总是返回false
            case (.cardTapped(let a), .cardTapped(let b)):
                return a == b
            case (.filterButtonTapped, .filterButtonTapped):
                return true
            case (.onAppear, .onAppear):
                return true
            case (.onDisappear, .onDisappear):
                return true
            case (.setTabActive(let a), .setTabActive(let b)):
                return a == b
            case (.dismissToast, .dismissToast):
                return true
            case (.fetchSearchSuggestions, .fetchSearchSuggestions):
                return true
            case (.searchButtonTapped, .searchButtonTapped):
                return true
            case (.refreshSearchPlaceholder, .refreshSearchPlaceholder):
                return true
            case (.loginButtonTapped, .loginButtonTapped):
                return true
            case (.profileButtonTapped, .profileButtonTapped):
                return true
            case (.delegate(let a), .delegate(let b)):
                return a == b
            case (._handleRefreshResult, ._handleRefreshResult):
                return false // 闭包无法比较，总是返回false
            case (._handleLoadMoreResult, ._handleLoadMoreResult):
                return false // 闭包无法比较，总是返回false
            case (._searchSuggestionsLoaded(let a), ._searchSuggestionsLoaded(let b)):
                return a == b
            default:
                return false
            }
        }
    }
    
    enum Result: Equatable {
        case success([DataRepositoryCore.RecommendCardItem])
        case failure(String)
    }

    /// 推荐功能相关错误（使用Repository层的错误类型）
    typealias RecommendError = DataRepositoryCore.PostRepository.RecommendCardError
    
    // MARK: - 数据获取方法

    /// 统一的数据获取方法 - 使用Repository层的转换逻辑和错误处理
    private func fetchContentList(pageNum: Int, keyword: String? = nil) async throws -> [DataRepositoryCore.RecommendCardItem] {
        let result: DataRepositoryCore.PostRepository.RecommendCardResult

        if let keyword = keyword, !keyword.isEmpty {
            // 搜索模式
            result = await postRepository.searchRecommendCardItemsWithResult(
                pageNum: pageNum,
                pageSize: Self.pageSize,
                keyword: keyword
            )
        } else {
            // 推荐模式
            result = await postRepository.getRecommendCardItemsWithResult(
                pageNum: pageNum,
                pageSize: Self.pageSize
            )
        }

        switch result {
        case .success(let items):
            return items
        case .failure(let error):
            throw error
        }
    }

    // MARK: - UI辅助方法

    /// 显示Toast消息并自动关闭
    private func showToastEffect(message: String) -> Effect<Action> {
        return .run { send in
            try await Task.sleep(for: .seconds(3))
            await send(.dismissToast)
        }
    }

    // MARK: - 搜索提示词管理

    /// 随机选择搜索提示词
    private func selectRandomPlaceholder(from suggestions: [String], excluding lastIndex: Int) -> (index: Int, placeholder: String)? {
        guard !suggestions.isEmpty else { return nil }

        var availableIndices = Array(0..<suggestions.count)

        // 如果有多个选项且上次使用过某个索引，则排除它
        if availableIndices.count > 1 && lastIndex >= 0 {
            availableIndices.removeAll { $0 == lastIndex }
        }

        guard !availableIndices.isEmpty else { return nil }

        let randomIndex = availableIndices.randomElement()!
        let selectedTag = suggestions[randomIndex]

        return (index: randomIndex, placeholder: selectedTag)
    }

    // MARK: - 初始化逻辑

    /// 页面初始化状态
    struct AppearContext {
        let isLoggedIn: Bool
        let shouldFetchSuggestions: Bool
        let itemsEmpty: Bool
        let hasSearchText: Bool
        let hasUserAvatar: Bool
        let isTabActive: Bool
        let hasInitialized: Bool
        let shouldForceRefresh: Bool

        init(from state: State) {
            self.isLoggedIn = state.user.isLoggedIn
            self.shouldFetchSuggestions = state.search.suggestions.isEmpty
            self.itemsEmpty = state.content.items.isEmpty
            self.hasSearchText = !state.search.searchText.isEmpty
            self.hasUserAvatar = state.user.avatarUrl != nil
            self.isTabActive = state.ui.isTabActive
            self.hasInitialized = state.ui.hasInitialized

            // 判断是否需要强制刷新（例如超过一定时间）
            if let timestamp = state.ui.initializationTimestamp {
                let timeSinceInit = Date().timeIntervalSince(timestamp)
                self.shouldForceRefresh = timeSinceInit > 300 // 5分钟后强制刷新
            } else {
                self.shouldForceRefresh = true
            }
        }

        /// 需要执行的初始化任务
        var initializationTasks: [InitializationTask] {
            var tasks: [InitializationTask] = []

            // 如果已经初始化过且不需要强制刷新，则跳过大部分任务
            if hasInitialized && !shouldForceRefresh {
                // 只执行轻量级的刷新任务
                if !hasSearchText {
                    tasks.append(.refreshPlaceholder)
                }
                return tasks
            }

            // 首次初始化或强制刷新时的完整任务

            // 只有在登录且没有头像时才获取头像
            if isLoggedIn && !hasUserAvatar {
                tasks.append(.fetchUserAvatar)
            }

            // 搜索提示词逻辑
            if shouldFetchSuggestions || shouldForceRefresh {
                tasks.append(.fetchSearchSuggestions)
            } else if !hasSearchText {
                // 只有在没有搜索文本时才刷新placeholder
                tasks.append(.refreshPlaceholder)
            }

            // 只有在列表为空且Tab活跃时才执行初始刷新
            if itemsEmpty && isTabActive {
                tasks.append(.initialRefresh)
            }

            return tasks
        }

        /// 是否需要执行任何初始化任务
        var needsInitialization: Bool {
            return !initializationTasks.isEmpty
        }

        /// 初始化摘要信息
        var summary: String {
            let taskDescriptions = initializationTasks.map(\.description)
            return taskDescriptions.isEmpty ? "无需初始化" : "需要执行: \(taskDescriptions.joined(separator: ", "))"
        }
    }

    /// 初始化任务类型
    enum InitializationTask: CaseIterable {
        case fetchUserAvatar
        case fetchSearchSuggestions
        case refreshPlaceholder
        case initialRefresh

        var description: String {
            switch self {
            case .fetchUserAvatar: return "获取用户头像"
            case .fetchSearchSuggestions: return "获取搜索提示词"
            case .refreshPlaceholder: return "刷新搜索占位符"
            case .initialRefresh: return "初始数据刷新"
            }
        }
    }

    /// 处理页面出现时的初始化逻辑
    private func handleAppearEffect(context: AppearContext) -> Effect<Action> {
        guard context.needsInitialization else {
            return .none
        }

        return .run { send in
            await executeInitializationTasks(context.initializationTasks, send: send)

            // 标记初始化完成
            await send(.binding(.set(\.ui.hasInitialized, true)))
            await send(.binding(.set(\.ui.initializationTimestamp, Date())))
        }
    }

    /// 执行初始化任务（支持并发和顺序执行）
    private func executeInitializationTasks(
        _ tasks: [InitializationTask],
        send: Send<Action>
    ) async {
        // 将任务分为可并发执行和需要顺序执行的
        let (concurrentTasks, sequentialTasks) = partitionTasks(tasks)

        // 并发执行独立任务
        if !concurrentTasks.isEmpty {
            await withTaskGroup(of: Void.self) { group in
                for task in concurrentTasks {
                    group.addTask {
                        await self.executeTask(task, send: send)
                    }
                }
            }
        }

        // 顺序执行依赖任务
        for task in sequentialTasks {
            await executeTask(task, send: send)
        }
    }

    /// 将任务分为并发和顺序执行
    private func partitionTasks(_ tasks: [InitializationTask]) -> (concurrent: [InitializationTask], sequential: [InitializationTask]) {
        var concurrent: [InitializationTask] = []
        var sequential: [InitializationTask] = []

        for task in tasks {
            switch task {
            case .fetchUserAvatar:
                // 获取头像可以并发执行
                concurrent.append(task)
            case .fetchSearchSuggestions, .refreshPlaceholder:
                // 搜索相关任务需要顺序执行
                sequential.append(task)
            case .initialRefresh:
                // 初始刷新应该在最后执行
                sequential.append(task)
            }
        }

        return (concurrent, sequential)
    }

    /// 执行单个初始化任务
    private func executeTask(_ task: InitializationTask, send: Send<Action>) async {
        do {
            switch task {
            case .fetchUserAvatar:
                if let avatarURL = await userRepository.getUserAvatarURL() {
                    await send(.binding(.set(\.user.avatarUrl, avatarURL)))
                }

            case .fetchSearchSuggestions:
                await send(.fetchSearchSuggestions)

            case .refreshPlaceholder:
                await send(.refreshSearchPlaceholder)

            case .initialRefresh:
                await send(.refresh(completion: { _ in }))
            }
        } catch {
            // 静默处理错误
        }
    }
    
    // 获取搜索提示词
    private func fetchSearchSuggestions() async -> [String] {
        if let tags = await characterRepository.searchWords(num: 1000, ) {
            return tags
        } else {
            return []
        }
    }
    
    var body: some ReducerOf<Self> {
        Reduce<State, Action> { state, action in
            switch action {
            case .binding:
                return .none
            case .onAppear:
                // 检查用户登录状态是否发生变化
                let previousLoginState = state.user.isLoggedIn
                state.user.isLoggedIn = userRepository.isLoggedIn()

                // 如果登录状态发生变化，重置初始化状态
                if previousLoginState != state.user.isLoggedIn {
                    state.ui.hasInitialized = false
                    state.ui.initializationTimestamp = nil
                }

                // 创建初始化上下文并执行初始化任务
                let context = AppearContext(from: state)
                return handleAppearEffect(context: context)
                
            case .fetchSearchSuggestions:
                // 获取搜索提示词
                return .run { send in
                    let suggestions = await fetchSearchSuggestions()
                    await send(._searchSuggestionsLoaded(suggestions))
                }
                .cancellable(id: "fetch-search-suggestions", cancelInFlight: true)
                
            case ._searchSuggestionsLoaded(let suggestions):
                // 保存搜索提示词，并随机选择一个作为当前提示词
                state.search.suggestions = suggestions
                return .send(.refreshSearchPlaceholder)

            case .refreshSearchPlaceholder:
                // 随机选择一个与上次不同的搜索提示词
                if let result = selectRandomPlaceholder(
                    from: state.search.suggestions,
                    excluding: state.search.lastUsedIndex
                ) {
                    state.search.lastUsedIndex = result.index
                    state.search.currentPlaceholder = result.placeholder
                }
                return .none
                
            case .searchButtonTapped:
                // 搜索逻辑已经在SearchBarView内部处理
                // 这里只需要执行搜索，不刷新placeholder（保持用户输入的文本）
                return .send(.refresh(completion: { _ in }))
                
            case .onDisappear:
                // 页面消失时可取消网络请求（如需启用，取消下行注释）
                // return .cancel(id: "recommend-network")
                return .none
            case let .refresh(completion):
                state.content.isRefreshing = true

                // 只有在列表为空时才显示加载状态
                if state.content.items.isEmpty {
                    state.content.pageState = .loading
                }

                // 不再清空现有数据，只有在成功获取新数据后才替换
                state.content.hasMore = true
                state.content.loadMoreFailed = false // 刷新时重置加载失败状态
                state.content.currentPage = 1 // 重置页码

                // 刷新时不再自动刷新搜索提示词，改为在屏幕可见时刷新
                return .run { [searchText = state.search.searchText] send in
                        do {
                            let items = try await fetchContentList(
                                pageNum: 1,
                                keyword: searchText.isEmpty ? nil : searchText
                            )
                            await send(._handleRefreshResult(.success(items), completion: completion))
                        } catch let error as RecommendError {
                            await send(._handleRefreshResult(.failure(error.localizedDescription), completion: completion))
                        } catch {
                            await send(._handleRefreshResult(.failure("未知错误"), completion: completion))
                        }
                    }
                    .cancellable(id: "recommend-network", cancelInFlight: true)
                
            case let ._handleRefreshResult(result, completion):
                state.content.isRefreshing = false
                switch result {
                case .success(let items):
                    // 成功获取数据后替换现有数据
                    state.content.items = items

                    // 只有在列表为空时才更新页面状态
                    if items.isEmpty {
                        state.content.pageState = .success
                        state.content.isEmptyList = true
                    } else if state.content.pageState == .loading {
                        // 如果之前是加载状态，更新为成功状态
                        state.content.pageState = .success
                        state.content.isEmptyList = false
                    }

                    state.content.hasMore = items.count == Self.pageSize
                    completion(.success)
                case .failure(let error):
                    // 如果有现有数据，保留现有数据，只显示toast
                    if !state.content.items.isEmpty {
                        state.ui.toastMessage = "刷新失败：\(error)"
                        completion(.failure)
                        return showToastEffect(message: error)
                    } else {
                        // 没有现有数据时，显示错误页面
                        state.content.pageState = .failed(error: error)
                        state.content.hasMore = false
                        state.content.isEmptyList = false
                        completion(.failure)
                    }
                }
                return .none
                
            case .dismissToast:
                state.ui.toastMessage = nil
                return .none

            case let .loadMore(completion):
                guard !state.content.isLoadingMore else {
                    completion(.failure)
                    return .none
                }

                state.content.isLoadingMore = true
                state.content.loadMoreFailed = false

                // 使用当前页码+1作为下一页
                let nextPage = state.content.currentPage + 1

                return .run { [searchText = state.search.searchText] send in
                    do {
                        let items = try await fetchContentList(
                            pageNum: nextPage,
                            keyword: searchText.isEmpty ? nil : searchText
                        )
                        await send(._handleLoadMoreResult(.success(items), completion: completion))
                    } catch let error as RecommendError {
                        await send(._handleLoadMoreResult(.failure(error.localizedDescription), completion: completion))
                    } catch {
                        await send(._handleLoadMoreResult(.failure("未知错误"), completion: completion))
                    }
                }
                .cancellable(id: "recommend-network-loadmore", cancelInFlight: true)
                
            case let ._handleLoadMoreResult(result, completion):
                state.content.isLoadingMore = false
                switch result {
                case .success(let items):
                    if items.isEmpty {
                        state.content.hasMore = false
                        completion(.noMore)
                    } else {
                        state.content.items += items
                        let hasNext = items.count == Self.pageSize
                        state.content.hasMore = hasNext

                        // 加载成功时更新当前页码
                        state.content.currentPage += 1

                        completion(hasNext ? .success : .noMore)
                    }
                case .failure(let error):
                    // 设置loadMoreFailed=true
                    state.content.loadMoreFailed = true
                    state.ui.toastMessage = "加载更多失败：\(error)"
                    completion(.failure)

                    return showToastEffect(message: error)
                }
                return .none
                
            case .cardTapped(let item):
                XLog.i("推荐卡片被点击 - ID: \(item.id), 标题: \(item.title)")
                return .none
            case .filterButtonTapped:
                return .none
            case .setTabActive(let active):
                let wasInactive = !state.ui.isTabActive
                state.ui.isTabActive = active

                // 如果从非活跃状态变为活跃状态，且搜索文本为空，则刷新搜索词
                if wasInactive && active && state.search.searchText.isEmpty && !state.search.suggestions.isEmpty {
                    return .send(.refreshSearchPlaceholder)
                }

                // if !active {
                //     // tab 不活跃时取消网络请求（如需启用，取消下行注释）
                //     return .cancel(id: "recommend-network")
                // }
                return .none
                
            case .loginButtonTapped:
                // 处理登录按钮点击，请求跳转到登录页面
                return .send(.delegate(.loginRequested(context: .guestLogin)))

            case .profileButtonTapped:
                // 处理个人资料按钮点击，请求跳转到个人信息页面
                return .send(.delegate(.navigateToProfile))
            
            case .delegate:
                // 委托动作由父组件处理
                return .none
            }
        }
        // ._printChanges() // 注释掉TCA调试日志，避免控制台输出过多Action信息
    }
} 
