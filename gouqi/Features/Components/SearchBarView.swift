import SwiftUI
import LogCore

/// 通用搜索栏组件，可复用于多个页面
/// - 参数 text: 绑定的搜索文本
/// - 参数 placeholder: 占位符文本（推荐搜索词）
/// - 参数 onCommit: 回车/搜索事件回调
struct SearchBarView: View {
    @Binding var text: String
    var placeholder: String
    var onCommit: (() -> Void)? = nil

    // 可选的外部焦点状态绑定，如果提供则使用外部控制
    var externalFocusBinding: Binding<Bool>?

    @FocusState private var isTextFieldFocused: Bool

    // 计算属性：优先使用外部绑定的焦点状态，否则使用内部状态
    private var effectiveFocusState: Bool {
        return externalFocusBinding?.wrappedValue ?? isTextFieldFocused
    }

    init(text: Binding<String>, placeholder: String, onCommit: (() -> Void)? = nil, isFocused: Binding<Bool>? = nil) {
        self._text = text
        self.placeholder = placeholder
        self.onCommit = onCommit
        self.externalFocusBinding = isFocused
    }

    var body: some View {
        HStack(spacing: 0) {
            // 显示占位符文本：当文本为空且未聚焦时显示
            ZStack(alignment: .leading) {
                if text.isEmpty && !effectiveFocusState {
                    Text(placeholder)
                        .foregroundColor(Color.white.opacity(0.56))
                        .padding(.leading, 12)

                }

                TextField("", text: $text, onCommit: {
                    performSearch()
                })
                .focused($isTextFieldFocused)
                .padding(.horizontal, 12)
                .frame(height: 40)
                .font(.system(size: 16))
                .foregroundColor(.white)
                .onChange(of: externalFocusBinding?.wrappedValue) { newValue in
                    Task { @MainActor in
                        if let newValue = newValue {
                            isTextFieldFocused = newValue
                        }
                    }
                }
            }

            Spacer(minLength: 0)
            Button(action: {
                performSearch()
            }) {
                Image(systemName: "magnifyingglass")
                    .resizable()
                    .frame(width: 18, height: 18)
                    .foregroundColor(Color.white.opacity(0.56))
            }
            .padding(.trailing, 12)
        }
        .background(Color("surface-Secondary-Button"))
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 15)
                .stroke(Color.white.opacity(0.24), lineWidth: 1)
        )
        .frame(height: 40)
        .onTapGesture {
            // 点击搜索框时聚焦到输入框
            if let externalBinding = externalFocusBinding {
                externalBinding.wrappedValue = true
            } else {
                isTextFieldFocused = true
            }
        }
    }
    
    private func performSearch() {
        // 如果文本为空，使用placeholder进行搜索
        if text.isEmpty {
            text = placeholder
        }

        // 执行搜索回调
        onCommit?()

        // 失去焦点
        if let externalBinding = externalFocusBinding {
            externalBinding.wrappedValue = false
        } else {
            isTextFieldFocused = false
        }
    }
}

#if DEBUG
struct SearchBarView_Previews: PreviewProvider {
    @State static var text = ""

    static var previews: some View {
        Group {
            // 空文本状态
            ZStack {
                Color("color-background").ignoresSafeArea()
                SearchBarView(text: $text, placeholder: "搜索角色")
                    .padding()
            }
            .previewDisplayName("空文本状态")

            // 推荐搜索词
            ZStack {
                Color("color-background").ignoresSafeArea()
                SearchBarView(text: $text, placeholder: "热门角色：小鸭鸭")
                .padding()
            }
            .previewDisplayName("推荐搜索词")

            // 有文本状态
            ZStack {
                Color("color-background").ignoresSafeArea()
                SearchBarView(text: .constant("已输入内容"), placeholder: "搜索角色")
                .padding()
            }
            .previewDisplayName("有文本状态")
        }
        .previewLayout(.sizeThatFits)
    }
}
#endif
