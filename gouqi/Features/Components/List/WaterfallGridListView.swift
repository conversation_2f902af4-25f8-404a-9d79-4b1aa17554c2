import SwiftUI
import LogCore
import UIKit

/// 加载更多结果
enum LoadMoreResult {
    case success
    case noMore
    case failure
}

enum RefreshResult {
    case success
    case noMore
    case failure
}

/// 双列瀑布流列表组件，支持刷新、加载更多、空列表，卡片宽度自适应
struct WaterfallGridListView<Item: Identifiable, CardView: View>: View {
    let items: [Item]
    let hasMore: Bool
    let loadMoreFailed: Bool
    let isLoadingContent: Bool // 内容加载状态
    let onLoadMore: (@escaping (LoadMoreResult) -> Void) -> Void
    let onRefresh: (@escaping (RefreshResult) -> Void) -> Void

    let cardView: (Item, CGFloat) -> CardView
    var itemSpacing: CGFloat = 12
    var horizontalPadding: CGFloat = 12
  
    @State private var headerRefreshing = false
    @State private var isLoading = false

    // 计算列分配
    private var columns: (left: [Item], right: [Item]) {
        var leftColumn = [Item]()
        var rightColumn = [Item]()

        for (index, item) in items.enumerated() {
            if index % 2 == 0 {
                leftColumn.append(item)
            } else {
                rightColumn.append(item)
            }
        }

        return (leftColumn, rightColumn)
    }
    



    var body: some View {
        GeometryReader { geometry in
            let cardWidth = (geometry.size.width - itemSpacing * 3) / 2



            ZStack {
                ScrollView {
                    ScrollViewReader { scrollProxy in
                        VStack(spacing: 0) {
                            // 使用 SwiftUI 原生的 HStack + 双 LazyVStack 实现带懒加载的瀑布流
                            let currentColumns = columns
                            HStack(alignment: .top, spacing: itemSpacing) {
                                // 左列 - 使用 LazyVStack 实现懒加载
                                LazyVStack(spacing: itemSpacing) {
                                    ForEach(Array(currentColumns.left.enumerated()), id: \.element.id) { index, item in
                                        cardView(item, cardWidth)
                                            .id("left-\(index)-\(item.id)")
                                    }
                                }

                                // 右列 - 使用 LazyVStack 实现懒加载
                                LazyVStack(spacing: itemSpacing) {
                                    ForEach(Array(currentColumns.right.enumerated()), id: \.element.id) { index, item in
                                        cardView(item, cardWidth)
                                            .id("right-\(index)-\(item.id)")
                                    }
                                }
                            }
                            .padding(.horizontal, itemSpacing)
                            

                            // 使用 RefreshFooter 组件实现上拉加载
                            if !items.isEmpty {
                                EmptyView()
                                    // .logInfo("[瀑布流][渲染] 渲染底部加载组件: isLoading=\(isLoading), hasMore=\(hasMore), loadMoreFailed=\(loadMoreFailed)" )
                                
                                RefreshFooter(refreshing: $isLoading, action: {
                                    XLog.i("[瀑布流][上拉] RefreshFooter的action回调被执行")
                                    loadMoreIfNeeded(source: "上拉加载")
                                }) {
                                    HStack {
                                        Spacer()
                                        
                                        if loadMoreFailed {
                                            Text("加载失败，上滑重试")
                                                .foregroundColor(.white)
                                                .font(.caption)
                                                .padding()
                                        } else if !hasMore {
                                            Text("没有更多内容了")
                                                .font(.caption)
                                                .foregroundColor(.white)
                                                .padding()
                                        } else {
                                            VStack(spacing: 6) {
                                                ProgressView()
                                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                                    .scaleEffect(1)
                                                // Text("正在加载...")
                                                //     .font(.caption)
                                                //     .foregroundColor(.white)
                                            }
                                            .padding()
                                        }
                                        
                                        Spacer()
                                    }
                                    .padding(.top, 10)
                                    .frame(height: 130, alignment: .top)
                                    // .background(Color.red)
                                    .onAppear {
                                        // XLog.i("[瀑布流][底部] 底部加载指示器出现: isLoading=\(isLoading)")
                                    }
                                }
                                .noMore(!hasMore)
                                .onAppear {
                                    // XLog.i("[瀑布流][底部] RefreshFooter组件出现")
                                }
                                // 移除预加载，只允许手动上拉触发
                                // .preload(offset: 100) // 提前100pt触发加载
                            }
                        }
                    }
                }
                .enableRefresh()
                .coordinateSpace(name: "scrollView")
                .refreshable {
                    // 使用 async/await 实现下拉刷新
                    XLog.i("[瀑布流][下拉] 下拉刷新触发")
                    headerRefreshing = true
                    await withCheckedContinuation { continuation in
                        onRefresh { result in
                            XLog.i("[瀑布流][下拉] 下拉刷新结果: \(result)")
                            headerRefreshing = false
                            
                            // 刷新后重置加载更多状态
                            if result == .success {
                                isLoading = false
                            }
                            
                            continuation.resume()
                        }
                    }
                }
                .onAppear {
                    // 设置刷新控件的颜色为白色
                    UIRefreshControl.appearance().tintColor = UIColor.white
                    // XLog.i("[瀑布流][初始] 组件出现，项目数量: \(items.count), hasMore: \(hasMore)")
                }
                // .enableRefresh() // 启用 Refresh 组件功能，激活上拉刷新
                // 注意：enableRefresh 暂不可用，但 RefreshFooter 仍然可以工作
                
                // 内容加载状态视图（标签切换时的加载状态）
                if items.isEmpty && isLoadingContent {
                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.2)
                        Text("加载中...")
                            .font(.system(size: 16))
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .frame(width: geometry.size.width, height: geometry.size.height)
                }

                // 空状态视图，放在ZStack中以便居中显示
                if items.isEmpty && !headerRefreshing && !isLoadingContent {
                    EmptyListView()
                        .frame(width: geometry.size.width, height: geometry.size.height)
                }

                // 显示下拉刷新加载中状态
                if headerRefreshing && items.isEmpty && !isLoadingContent {
                    ProgressView("刷新中...")
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .frame(width: geometry.size.width, height: geometry.size.height)
                }
            }
        }
    }
    
    // 加载更多方法
    private func loadMoreIfNeeded(source: String = "未知来源") {
        // 如果已经在加载中或没有更多数据，不再触发新的加载
        guard  hasMore else {
            // XLog.i("[瀑布流][跳过加载] 触发来源: \(source), 当前加载状态=\(isLoading), hasMore=\(hasMore)")
            return
        }
        
        // 设置加载状态为loading
        XLog.i("[瀑布流][开始加载] 触发来源: \(source), 项目数量: \(items.count)")
        isLoading = true
        
  
            // 执行加载操作
            onLoadMore { result in
                XLog.i("[瀑布流][加载结果] 触发来源: \(source), 结果: \(result), 原状态: isLoading=\(isLoading)")
                
                switch result {
                case .success:
                    XLog.i("[瀑布流][加载成功] 重置加载状态")
                    isLoading = false
                case .noMore:
                    XLog.i("[瀑布流][无更多数据] 重置加载状态")
                    isLoading = false
                case .failure:
                    XLog.i("[瀑布流][加载失败] 重置加载状态")
                    isLoading = false
                }
            }
    }
}





